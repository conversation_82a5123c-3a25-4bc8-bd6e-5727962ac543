# ServiceAccounts for all applications
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager
  namespace: cert-manager
automountServiceAccountToken: true
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: cainjector
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cainjector
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-cainjector
  namespace: cert-manager
automountServiceAccountToken: true
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: webhook
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: webhook
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-webhook
  namespace: cert-manager
automountServiceAccountToken: true
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: default
  namespace: cert-manager
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: admin-user
  namespace: kubernetes-dashboard
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: default
  namespace: kubernetes-dashboard
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard
  namespace: kubernetes-dashboard
