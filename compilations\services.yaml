﻿# Services for all applications
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: cainjector
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cainjector
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-cainjector
  namespace: cert-manager
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: http-metrics
    port: 9402
  selector:
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cainjector
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager
  namespace: cert-manager
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: tcp-prometheus-servicemonitor
    port: 9402
    targetPort: http-metrics
  selector:
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: webhook
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: webhook
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-webhook
  namespace: cert-manager
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: https
    port: 443
    targetPort: https
  - name: metrics
    port: 9402
    targetPort: http-metrics
  selector:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: webhook
---
apiVersion: v1
kind: Service
metadata:
  labels:
    k8s-app: dashboard-metrics-scraper
  name: dashboard-metrics-scraper
  namespace: kubernetes-dashboard
spec:
  clusterIP: ************
  clusterIPs:
  - ************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - port: 8000
  selector:
    k8s-app: dashboard-metrics-scraper
---
apiVersion: v1
kind: Service
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard
  namespace: kubernetes-dashboard
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - port: 443
    targetPort: 8443
  selector:
    k8s-app: kubernetes-dashboard
---
apiVersion: v1
kind: Service
metadata:
  name: n8n-service
  namespace: n8n
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - port: 5678
  selector:
    app: n8n-main
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: n8n
spec:
  clusterIP: ***********
  clusterIPs:
  - ***********
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - port: 6379
  selector:
    app: redis
---
apiVersion: v1
kind: Service
metadata:
  name: pgadmin-service
  namespace: postgres
spec:
  clusterIP: ************
  clusterIPs:
  - ************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - port: 80
  selector:
    app: pgadmin
