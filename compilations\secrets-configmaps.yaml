﻿# Secrets and ConfigMaps for all applications
---
apiVersion: v1
kind: Secret
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-certs
  namespace: kubernetes-dashboard
type: Opaque
---
apiVersion: v1
data:
  csrf: DNwYIHKyT5kQOr4uBzY3c81yMd7NpDq8BwsEcpNCAA8DOVo7WdPuhTLUEe3DKD2dqHhqClReQs0a30uDAF3apL3BFAy0ys901sWysXHy+SVq8LmBPjB1YBkA7ma3HPjo2tGpH6O3hWA0FPSz3zHFiY8LnqDm1q5zbY6pdh20g/XW99PiPUpJo+iJ8czBgf3sZvt/uH6nQvINY0WMnLnI2vGHBqAtkyR2BIam63h2/vzbbEDs2H3j1pQMLZFoYYDeB3OraDzJwI/UcBuXg9EkDcaCs0KdbMd/Xh2rcd+nhIkxSUtUqMLSWUaurusJ31SJsfbwUk2OPNiYr5i/yM1bGg==
kind: Secret
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-csrf
  namespace: kubernetes-dashboard
type: Opaque
---
apiVersion: v1
data:
  priv: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  pub: LS0tLS1CRUdJTiBSU0EgUFVCTElDIEtFWS0tLS0tCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBcER2RmVmUmg1NUphZWVBM0pHVnAKa0Y3WGhSbzQxcVJzMkJ2NnZFWmlINVdXblk4S2doWGU2cForSDZheDEzNUphK05kNk9haVR1ZkNCMWxrYUVsOQo3a3hHSUpncERYcVRETUQ1YmpkSE91SE9za1p4aDlKNlV1eDYyZ2QzWm1GUGZJTjk3dS9RQkdKVXJ6L1h0QnVsCmR5dUtvaC9oamp5QS9QTldWazNMS1pJNEpkS1d2VEZHS3Z3U1lvdTN1RnVsa0phaEVEZGVFN1RQekZTaUJIV3oKakNKbDhCd2xjcnIxTER2Q0ZvV2JiUi9nMExBRUprNjJhNEhOSzY1NHkyUkxjWlFMamNaVmlhaUJUZ2lCZ1VGMQo5OVBEWlJndHFvVlpiQmtDcXpSNEovWFhmVWZwSnJpQ01yNUxaeUp1Tm8vRlJ5aENEdXVnR0ora3dnUmdXRU5BCmJRSURBUUFCCi0tLS0tRU5EIFJTQSBQVUJMSUMgS0VZLS0tLS0K
kind: Secret
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-key-holder
  namespace: kubernetes-dashboard
type: Opaque
---
apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: dashboard.locke.cz
    cert-manager.io/certificate-name: dashboard-cert
    cert-manager.io/common-name: dashboard.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: ""
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: dashboard-tls
  namespace: kubernetes-dashboard
type: kubernetes.io/tls
---
apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: n8n-admin.locke.cz
    cert-manager.io/certificate-name: n8n-admin-tls
    cert-manager.io/common-name: n8n-admin.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: n8n-admin-tls
  namespace: n8n
type: kubernetes.io/tls
---
apiVersion: v1
data:
  db-password: N3hwNGpoQnMzSENxLStF
kind: Secret
metadata:
  name: n8n-db-secret
  namespace: n8n
type: Opaque
---
apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: n8n.locke.cz
    cert-manager.io/certificate-name: n8n-tls-secret-prod
    cert-manager.io/common-name: n8n.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-prod
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: n8n-tls-secret-prod
  namespace: n8n
type: kubernetes.io/tls
---
apiVersion: v1
data:
  tls.crt: 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
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: n8n-webhook.locke.cz
    cert-manager.io/certificate-name: n8n-webhook-tls
    cert-manager.io/common-name: n8n-webhook.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: n8n-webhook-tls
  namespace: n8n
type: kubernetes.io/tls
---
apiVersion: v1
data:
  PGADMIN_DEFAULT_EMAIL: YXJ0aHVyQGxvY2tlLmN6
  PGADMIN_DEFAULT_PASSWORD: U3Ryb25nUGFzc3dvcmQxMjM=
kind: Secret
metadata:
  name: pgadmin-secret
  namespace: postgres
type: Opaque
---
apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: pgadmin.locke.cz
    cert-manager.io/certificate-name: pgadmin-tls
    cert-manager.io/common-name: pgadmin.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: pgadmin-tls
  namespace: postgres
type: kubernetes.io/tls
