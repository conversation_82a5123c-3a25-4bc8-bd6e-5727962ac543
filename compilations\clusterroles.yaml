﻿# ClusterRoles for all applications
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: cert-manager
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-approve:cert-manager-io
rules:
- apiGroups:
  - cert-manager.io
  resourceNames:
  - issuers.cert-manager.io/*
  - clusterissuers.cert-manager.io/*
  resources:
  - signers
  verbs:
  - approve
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cainjector
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cainjector
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-cainjector
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - get
  - create
  - update
  - patch
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - validatingwebhookconfigurations
  - mutatingwebhookconfigurations
  verbs:
  - get
  - list
  - watch
  - update
  - patch
- apiGroups:
  - apiregistration.k8s.io
  resources:
  - apiservices
  verbs:
  - get
  - list
  - watch
  - update
  - patch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - watch
  - update
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-certificates
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificates/status
  - certificaterequests
  - certificaterequests/status
  verbs:
  - update
  - patch
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  - clusterissuers
  - issuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - cert-manager.io
  resources:
  - certificates/finalizers
  - certificaterequests/finalizers
  verbs:
  - update
- apiGroups:
  - acme.cert-manager.io
  resources:
  - orders
  verbs:
  - create
  - delete
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - delete
  - patch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
    rbac.authorization.k8s.io/aggregate-to-cluster-reader: "true"
  name: cert-manager-cluster-view
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - clusterissuers
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-clusterissuers
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - clusterissuers
  - clusterissuers/status
  verbs:
  - update
  - patch
- apiGroups:
  - cert-manager.io
  resources:
  - clusterissuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: cert-manager
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-certificatesigningrequests
rules:
- apiGroups:
  - certificates.k8s.io
  resources:
  - certificatesigningrequests
  verbs:
  - get
  - list
  - watch
  - update
- apiGroups:
  - certificates.k8s.io
  resources:
  - certificatesigningrequests/status
  verbs:
  - update
  - patch
- apiGroups:
  - certificates.k8s.io
  resourceNames:
  - issuers.cert-manager.io/*
  - clusterissuers.cert-manager.io/*
  resources:
  - signers
  verbs:
  - sign
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-dns01-controller-challenges
rules:
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  - challenges/status
  verbs:
  - update
  - patch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - cert-manager.io
  resources:
  - issuers
  - clusterissuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
  name: cert-manager-edit
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  - issuers
  verbs:
  - create
  - delete
  - deletecollection
  - patch
  - update
- apiGroups:
  - cert-manager.io
  resources:
  - certificates/status
  verbs:
  - update
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  - orders
  verbs:
  - create
  - delete
  - deletecollection
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-http01-controller-challenges
rules:
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  - challenges/status
  verbs:
  - update
  - patch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - cert-manager.io
  resources:
  - issuers
  - clusterissuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - pods
  - services
  verbs:
  - get
  - list
  - watch
  - create
  - delete
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - get
  - list
  - watch
  - create
  - delete
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes
  verbs:
  - get
  - list
  - watch
  - create
  - delete
  - update
- apiGroups:
  - route.openshift.io
  resources:
  - routes/custom-host
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-ingress-shim
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  verbs:
  - create
  - update
  - delete
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  - issuers
  - clusterissuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/finalizers
  verbs:
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gateways
  - httproutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gateways/finalizers
  - httproutes/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-issuers
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - issuers
  - issuers/status
  verbs:
  - update
  - patch
- apiGroups:
  - cert-manager.io
  resources:
  - issuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-controller-orders
rules:
- apiGroups:
  - acme.cert-manager.io
  resources:
  - orders
  - orders/status
  verbs:
  - update
  - patch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - orders
  - challenges
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - cert-manager.io
  resources:
  - clusterissuers
  - issuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  verbs:
  - create
  - delete
- apiGroups:
  - acme.cert-manager.io
  resources:
  - orders/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    rbac.authorization.k8s.io/aggregate-to-cluster-reader: "true"
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
    rbac.authorization.k8s.io/aggregate-to-view: "true"
  name: cert-manager-view
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  - issuers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - acme.cert-manager.io
  resources:
  - challenges
  - orders
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: webhook
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: webhook
    app.kubernetes.io/version: v1.18.1
  name: cert-manager-webhook:subjectaccessreviews
rules:
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard
rules:
- apiGroups:
  - metrics.k8s.io
  resources:
  - pods
  - nodes
  verbs:
  - get
  - list
  - watch
