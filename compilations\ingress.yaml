﻿# Ingress resources for all applications
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/whitelist-source-range: *********/24,*********/24
  name: kubernetes-dashboard
  namespace: kubernetes-dashboard
spec:
  ingressClassName: nginx
  rules:
  - host: dashboard.locke.cz
    http:
      paths:
      - backend:
          service:
            name: kubernetes-dashboard
            port:
              number: 443
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - dashboard.locke.cz
    secretName: dashboard-tls
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
    nginx.ingress.kubernetes.io/whitelist-source-range: *********/24,*********/24
  name: n8n-admin-ingress
  namespace: n8n
spec:
  ingressClassName: nginx
  rules:
  - host: n8n-admin.locke.cz
    http:
      paths:
      - backend:
          service:
            name: n8n-service
            port:
              number: 5678
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - n8n-admin.locke.cz
    secretName: n8n-admin-tls
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
  name: n8n-webhook-ingress
  namespace: n8n
spec:
  ingressClassName: nginx
  rules:
  - host: n8n-webhook.locke.cz
    http:
      paths:
      - backend:
          service:
            name: n8n-service
            port:
              number: 5678
        path: /webhook/
        pathType: Prefix
  tls:
  - hosts:
    - n8n-webhook.locke.cz
    secretName: n8n-webhook-tls
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
    nginx.ingress.kubernetes.io/whitelist-source-range: *********/24,*********/24
  name: pgadmin-ingress
  namespace: postgres
spec:
  ingressClassName: nginx
  rules:
  - host: pgadmin.locke.cz
    http:
      paths:
      - backend:
          service:
            name: pgadmin-service
            port:
              number: 80
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - pgadmin.locke.cz
    secretName: pgadmin-tls
